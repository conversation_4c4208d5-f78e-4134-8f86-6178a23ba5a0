/**
 * 结构化数据验证脚本
 * 用于验证生成的JSON-LD结构化数据是否符合Schema.org标准
 */

const { createHomepageSchema } = require('../components/StructuredData');

// 测试数据
const testData = {
  mainGame: {
    name: "Merge Rot",
    description: "Play the ultimate merge rot and brainrot merge game online! Drop Italian brainrot fellas, merge crazy characters, and create viral memes. No download required!",
    url: "https://mergerotgame.com",
    image: "https://mergerotgame.com/images/merge-rot.avif",
    genre: ["Puzzle", "Casual", "Merge Game", "Brain Training"]
  },
  faqs: [
    {
      question: "What is merge rot and brainrot merge?",
      answer: "Merge rot is the viral brainrot merge game where you drop Italian brainrot fellas and meme characters into a cup, watching them merge into bigger, crazier versions!"
    },
    {
      question: "How do you play brainrot merge games?",
      answer: "Simply drag and drop brainrot characters! When two identical items collide, they merge into epic new forms. It's the ultimate brain rot merge experience!"
    }
  ],
  gameList: {
    games: [
      {
        name: "Merge Brainrot",
        description: "The original brainrot merge experience with Italian fellas and viral meme characters",
        url: "https://mergerotgame.com/games/merge-games/merge-brainrot",
        genre: ["Puzzle", "Casual", "Merge Game"]
      },
      {
        name: "Italian Brainrot 2048",
        description: "Classic 2048 gameplay with Italian brainrot characters and themes",
        url: "https://mergerotgame.com/games/merge-games/italian-brainrot-2048",
        genre: ["Puzzle", "Strategy", "Number Game"]
      }
    ],
    listName: "热门推荐游戏",
    listDescription: "发现更多精彩的脑洞合成游戏"
  }
};

// 验证函数
function validateStructuredData() {
  try {
    console.log('🔍 开始验证结构化数据...\n');
    
    // 生成结构化数据
    const schema = createHomepageSchema(testData);
    
    // 基础验证
    console.log('✅ 基础结构验证:');
    console.log(`   - @context: ${schema['@context']}`);
    console.log(`   - @graph 项目数量: ${schema['@graph'].length}`);
    
    // 验证每个图谱项目
    console.log('\n📊 图谱项目验证:');
    schema['@graph'].forEach((item, index) => {
      console.log(`   ${index + 1}. ${item['@type']} - ${item.name || item['@id'] || 'N/A'}`);
      
      // 验证必填字段
      if (item['@type'] === 'WebSite') {
        console.log(`      ✓ URL: ${item.url}`);
        console.log(`      ✓ 描述: ${item.description ? '已设置' : '缺失'}`);
      }
      
      if (item['@type'] === 'Game') {
        console.log(`      ✓ 游戏名称: ${item.name}`);
        console.log(`      ✓ 价格: ${item.offers.price} ${item.offers.priceCurrency}`);
        console.log(`      ✓ 评分: ${item.aggregateRating.ratingValue}/5`);
      }
      
      if (item['@type'] === 'FAQPage') {
        console.log(`      ✓ FAQ数量: ${item.mainEntity.length}`);
      }
      
      if (item['@type'] === 'ItemList') {
        console.log(`      ✓ 游戏列表项目: ${item.numberOfItems}`);
      }
    });
    
    // JSON格式验证
    console.log('\n🔧 JSON格式验证:');
    const jsonString = JSON.stringify(schema, null, 2);
    console.log(`   ✓ JSON大小: ${(jsonString.length / 1024).toFixed(2)} KB`);
    console.log(`   ✓ 格式有效: ${jsonString.length > 0 ? '是' : '否'}`);
    
    // 输出完整的JSON-LD
    console.log('\n📄 生成的JSON-LD结构化数据:');
    console.log('```json');
    console.log(jsonString);
    console.log('```');
    
    console.log('\n✅ 验证完成！');
    console.log('\n📋 下一步操作:');
    console.log('1. 复制上面的JSON-LD到 https://validator.schema.org/ 进行验证');
    console.log('2. 使用 https://search.google.com/test/rich-results 测试Rich Results');
    console.log('3. 在Google Search Console中监控结构化数据报告');
    
  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    console.error(error.stack);
  }
}

// 运行验证
if (require.main === module) {
  validateStructuredData();
}

module.exports = { validateStructuredData };
