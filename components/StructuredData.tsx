import React from 'react';

interface StructuredDataProps {
  data: object;
}

/**
 * 结构化数据组件
 * 用于在页面中添加 JSON-LD 结构化数据
 */
export function StructuredData({ data }: StructuredDataProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data, null, 2)
      }}
    />
  );
}

/**
 * 创建文章结构化数据
 */
export function createArticleSchema({
  headline,
  description,
  image,
  datePublished,
  dateModified,
  url,
  keywords = [],
  wordCount,
  articleSection = "Gaming Culture"
}: {
  headline: string;
  description: string;
  image: string;
  datePublished: string;
  dateModified?: string;
  url: string;
  keywords?: string[];
  wordCount?: number;
  articleSection?: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "@id": `${url}#article`,
    headline,
    description,
    image: {
      "@type": "ImageObject",
      url: image,
      width: 1200,
      height: 630
    },
    author: {
      "@type": "Organization",
      name: "Merge Rot",
      url: "https://mergerotgame.com"
    },
    publisher: {
      "@type": "Organization",
      name: "Merge Rot",
      url: "https://mergerotgame.com",
      logo: {
        "@type": "ImageObject",
        url: "https://mergerotgame.com/logo.png",
        width: 200,
        height: 60
      }
    },
    datePublished,
    dateModified: dateModified || datePublished,
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": url
    },
    articleSection,
    keywords,
    ...(wordCount && { wordCount }),
    inLanguage: "en-US"
  };
}

/**
 * 创建FAQ结构化数据
 */
export function createFAQSchema(faqs: Array<{ question: string; answer: string }>, url: string) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "@id": `${url}#faq`,
    mainEntity: faqs.map(faq => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer
      }
    }))
  };
}

/**
 * 创建面包屑导航结构化数据
 */
export function createBreadcrumbSchema(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "@id": `${breadcrumbs[breadcrumbs.length - 1].url}#breadcrumb`,
    itemListElement: breadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  };
}

/**
 * 创建游戏列表结构化数据
 */
export function createGameListSchema(games: Array<{
  name: string;
  description: string;
  url: string;
  genre: string[];
}>, listName: string, listDescription: string) {
  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    name: listName,
    description: listDescription,
    numberOfItems: games.length,
    itemListElement: games.map((game, index) => ({
      "@type": "Game",
      position: index + 1,
      name: game.name,
      description: game.description,
      url: game.url,
      genre: game.genre,
      gamePlatform: "Web Browser",
      operatingSystem: "Any",
      applicationCategory: "Game",
      offers: {
        "@type": "Offer",
        price: "0",
        priceCurrency: "USD",
        availability: "https://schema.org/InStock"
      }
    }))
  };
}

/**
 * 创建视频结构化数据
 */
export function createVideoSchema({
  name,
  description,
  thumbnailUrl,
  embedUrl,
  uploadDate,
  duration,
  contentUrl,
  url
}: {
  name: string;
  description: string;
  thumbnailUrl: string;
  embedUrl: string;
  uploadDate: string;
  duration: string;
  contentUrl: string;
  url: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "VideoObject",
    "@id": `${url}#video`,
    name,
    description,
    thumbnailUrl,
    embedUrl,
    uploadDate,
    duration,
    contentUrl
  };
}

/**
 * 创建组织结构化数据
 */
export function createOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://mergerotgame.com#organization",
    name: "Merge Rot",
    url: "https://mergerotgame.com",
    logo: {
      "@type": "ImageObject",
      url: "https://mergerotgame.com/logo.png",
      width: 200,
      height: 60
    },
    sameAs: [
      "https://twitter.com/xwlAa0s8bB7Bpvt"
    ],
    description: "Free online games platform featuring Italian brainrot and viral meme games"
  };
}

/**
 * 创建完整的页面结构化数据图谱
 */
export function createPageSchema({
  article,
  faqs,
  breadcrumbs,
  video,
  gameList
}: {
  article?: ReturnType<typeof createArticleSchema>;
  faqs?: Array<{ question: string; answer: string }>;
  breadcrumbs?: Array<{ name: string; url: string }>;
  video?: Parameters<typeof createVideoSchema>[0];
  gameList?: {
    games: Array<{
      name: string;
      description: string;
      url: string;
      genre: string[];
    }>;
    listName: string;
    listDescription: string;
  };
}) {
  const graph: any[] = [];

  if (article) {
    graph.push(article);
  }

  if (faqs && faqs.length > 0) {
    graph.push(createFAQSchema(faqs, article?.["@id"]?.replace("#article", "") || ""));
  }

  if (video) {
    graph.push(createVideoSchema(video));
  }

  if (breadcrumbs && breadcrumbs.length > 0) {
    graph.push(createBreadcrumbSchema(breadcrumbs));
  }

  if (gameList) {
    graph.push(createGameListSchema(gameList.games, gameList.listName, gameList.listDescription));
  }

  // 总是包含组织信息
  graph.push(createOrganizationSchema());

  return {
    "@context": "https://schema.org",
    "@graph": graph
  };
}

export default StructuredData;
